/**
 * @fileoverview HTTP controller for user-related API endpoints.
 * This module handles HTTP requests and responses for user operations,
 * acting as the entry point for user-related API calls in the Next.js application.
 */

import { NextRequest, NextResponse } from 'next/server'
import { CreateUserUseCase } from '../use-cases/CreateUserUseCase'
import { CreateUserRequest, UserRole } from '../types/entities/User'

/**
 * HTTP controller for user operations.
 *
 * @description Handles HTTP requests for user-related operations.
 * Responsible for request parsing, validation, use case coordination,
 * and response formatting. Follows the Controller pattern in Clean Architecture.
 *
 * @example
 * ```typescript
 * const controller = new UserController(createUserUseCase)
 * const response = await controller.create(request)
 * ```
 */
export class UserController {
  /**
   * Creates a new UserController instance.
   *
   * @param createUserUseCase - Use case for creating users
   */
  constructor(private createUserUseCase: CreateUserUseCase) {}

  /**
   * Handles HTTP POST requests to create a new user.
   *
   * @param request - Next.js request object containing user data
   * @returns Promise resolving to HTTP response with created user or error
   *
   * @example
   * ```typescript
   * // Request body should contain:
   * // {
   * //   "email": "<EMAIL>",
   * //   "password": "securePassword123",
   * //   "role": "ENGINEER"
   * // }
   * const response = await controller.create(request)
   * ```
   */
  async create(request: NextRequest): Promise<NextResponse> {
    try {
      // Parsear el body de la request
      const body = await this.parseRequestBody(request)

      // Validar estructura básica
      this.validateRequestBody(body)

      // Extraer datos del request (safe to cast after validation)
      const bodyObj = body as Record<string, unknown>
      const createUserRequest: CreateUserRequest = {
        email: bodyObj.email as string,
        password: bodyObj.password as string,
        role: bodyObj.role as UserRole
      }

      // Ejecutar use case
      const result = await this.createUserUseCase.execute(createUserRequest)

      // Retornar respuesta exitosa
      return NextResponse.json(result, { status: 201 })

    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * Parses the JSON body from the HTTP request.
   *
   * @param request - Next.js request object
   * @returns Promise resolving to parsed JSON object
   * @throws Error if JSON parsing fails
   * @private
   */
  private async parseRequestBody(request: NextRequest): Promise<unknown> {
    try {
      return await request.json()
    } catch {
      throw new Error('Invalid JSON')
    }
  }

  /**
   * Validates the basic structure and required fields of the request body.
   *
   * @param body - Parsed request body object
   * @throws Error if validation fails
   * @private
   */
  private validateRequestBody(body: unknown): void {
    if (!body || typeof body !== 'object') {
      throw new Error('Request body is required')
    }

    const bodyObj = body as Record<string, unknown>

    if (!bodyObj.email) {
      throw new Error('Email is required')
    }

    if (!bodyObj.password) {
      throw new Error('Password is required')
    }

    if (!bodyObj.role) {
      throw new Error('Role is required')
    }
  }

  /**
   * Handles errors and converts them to appropriate HTTP responses.
   *
   * @param error - Error object or unknown error
   * @returns NextResponse with appropriate status code and error message
   * @private
   */
  private handleError(error: unknown): NextResponse {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    // Errores de validación y lógica de negocio
    if (this.isClientError(errorMessage)) {
      return NextResponse.json(
        { error: errorMessage },
        { status: 400 }
      )
    }

    // Errores del servidor
    console.error('Unexpected error in UserController:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }

  /**
   * Determines if an error should be treated as a client error (4xx) or server error (5xx).
   *
   * @param errorMessage - Error message to analyze
   * @returns True if it's a client error, false if it's a server error
   * @private
   */
  private isClientError(errorMessage: string): boolean {
    const clientErrorPatterns = [
      'required',
      'Invalid',
      'already exists',
      'not found',
      'must be',
      'format'
    ]

    return clientErrorPatterns.some(pattern =>
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    )
  }
}
