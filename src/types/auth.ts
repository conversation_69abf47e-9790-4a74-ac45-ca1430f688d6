/**
 * @fileoverview Type extensions for Auth.js to include custom user properties.
 * This module extends the default Auth.js types to include the user role
 * in sessions, tokens, and user objects.
 */

import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"
import { UserRole } from "./entities/User"

/**
 * Extend the default session user to include role
 */
declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: UserRole
    } & DefaultSession["user"]
  }

  interface User extends DefaultUser {
    role: UserRole
  }
}

/**
 * Extend the JWT token to include user id and role
 */
declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    id: string
    role: UserRole
  }
}
